import { prisma } from "@/prisma/prisma";
import { Prisma, ReconciliationStatus } from "@/prisma/generated";
import { ResultAsync } from "neverthrow";
import { differenceInDays, startOfDay, subDays } from "date-fns";
import { textUtils } from "@/modules/core/utils/textUtils";

export interface ReconciliationMatch {
  invoiceId: string;
  transferId: string;
  confidenceScore: number;
  matchReason: string;
}

export interface DetailedMatchScore {
  totalScore: number;
  maxPossibleScore: number;
  breakdown: {
    amount: {
      score: number;
      maxScore: number;
      reason: string;
      details: string;
    };
    date: {
      score: number;
      maxScore: number;
      reason: string;
      details: string;
    };
    vendor: {
      score: number;
      maxScore: number;
      reason: string;
      details: string;
    };
  };
  overallReason: string;
}

export interface ReconciliationConfig {
  // Maximum days difference between invoice date and transfer date
  maxDaysDifference: number;
  // Minimum confidence score to auto-match
  minAutoMatchConfidence: number;
  // Amount tolerance percentage (e.g., 0.01 for 1%)
  amountToleranceFraction: number;
}

export const DEFAULT_CONFIG: ReconciliationConfig = {
  maxDaysDifference: 60,
  minAutoMatchConfidence: 90,
  amountToleranceFraction: 0.02, // 2%
};

/**
 * Calculate detailed confidence score breakdown for a potential match between invoice and transfer
 */
export function calculateDetailedConfidenceScore(
  invoice: {
    date: Date;
    amountGross: Prisma.Decimal;
    currencyCode: string;
    vendorName?: string | null;
  },
  transfer: {
    amount: Prisma.Decimal;
    currencyCode: string;
    counterparty?: string | null;
    description?: string | null;
  },
  transaction: { executedAt: Date },
  config: ReconciliationConfig
): DetailedMatchScore {
  const breakdown = {
    amount: { score: 0, maxScore: 50, reason: "", details: "" },
    date: { score: 0, maxScore: 30, reason: "", details: "" },
    vendor: { score: 0, maxScore: 30, reason: "", details: "" },
  };

  const invoiceAmount = Math.abs(Number(invoice.amountGross));
  const transferAmount = Math.abs(Number(transfer.amount));
  const amountDiff = Math.abs(invoiceAmount - transferAmount);

  const normalizedInvoiceDate = startOfDay(invoice.date);
  const normalizedTransferDate = startOfDay(transaction.executedAt);

  // Check if invoice date is after transfer date (invalid)
  if (normalizedInvoiceDate.getTime() > normalizedTransferDate.getTime()) {
    return {
      totalScore: 0,
      maxPossibleScore: 110,
      breakdown,
      overallReason: "invoice_date_after_transfer_date",
    };
  }

  // Amount matching (50 points max)
  if (amountDiff === 0) {
    breakdown.amount.score = 50;
    breakdown.amount.reason = "exact_amount";
    breakdown.amount.details = "Perfect amount match";
  } else if (amountDiff <= config.amountToleranceFraction * invoiceAmount) {
    breakdown.amount.score = 40;
    breakdown.amount.reason = "amount_within_tolerance";
    breakdown.amount.details = `Within ${(config.amountToleranceFraction * 100).toFixed(1)}% tolerance`;
  } else if (amountDiff <= invoiceAmount * 0.03) {
    breakdown.amount.score = 20;
    breakdown.amount.reason = "amount_close";
    breakdown.amount.details = "Within 3% difference";
  } else {
    breakdown.amount.score = 0;
    breakdown.amount.reason = "amount_not_close";
    breakdown.amount.details = `Difference: ${amountDiff.toFixed(2)}`;
    return {
      totalScore: 0,
      maxPossibleScore: 110,
      breakdown,
      overallReason: "amount_not_close",
    };
  }

  const amountScoreFraction = invoiceAmount > transferAmount ? transferAmount / invoiceAmount : invoiceAmount / transferAmount;
  const amountScore = 50 * (Math.pow(amountScoreFraction, 4));
  breakdown.amount.score = amountScore;

  // Date proximity (30 points max)
  const daysDiff = Math.abs((invoice.date.getTime() - transaction.executedAt.getTime()) / (1000 * 60 * 60 * 24));

  if (daysDiff <= 1) {
    breakdown.date.score = 30;
    breakdown.date.reason = "same_date";
    breakdown.date.details = daysDiff === 0 ? "Same day" : "Next day";
  } else {
    const remainingRange = config.maxDaysDifference;
    const scale = (remainingRange - daysDiff) / remainingRange;
    breakdown.date.score = 30 * Math.max(scale, 0);
    breakdown.date.reason = "within_max_days";
    breakdown.date.details = `${Math.round(daysDiff)} days difference`;
  }

  // Vendor name similarity (30 points max)
  if (invoice.vendorName) {
    const searchText = [transfer.description
      // , transfer.counterparty
    ].filter(Boolean).join(" ");
    const sim = textUtils.vendorNameSimilarity(invoice.vendorName, searchText);
    breakdown.vendor.score = 30 * sim;

    if (sim >= 0.9) {
      breakdown.vendor.reason = "vendor_exact";
      breakdown.vendor.details = "High similarity match";
    } else if (sim >= 0.5) {
      breakdown.vendor.reason = "vendor_fuzzy";
      breakdown.vendor.details = `${(sim * 100).toFixed(0)}% similarity`;
    } else {
      breakdown.vendor.reason = "vendor_no_match";
      breakdown.vendor.details = `${(sim * 100).toFixed(0)}% similarity`;
    }
  } else {
    breakdown.vendor.reason = "no_vendor_name";
    breakdown.vendor.details = "No vendor name to compare";
  }

  const totalScore = breakdown.amount.score + breakdown.date.score + breakdown.vendor.score;
  const reasons = [breakdown.amount.reason, breakdown.date.reason, breakdown.vendor.reason].filter(Boolean);

  return {
    totalScore: Math.min(totalScore, 100),
    maxPossibleScore: 110,
    breakdown,
    overallReason: reasons.join(", "),
  };
}

/**
 * Calculate confidence score for a potential match between invoice and transfer
 */
function calculateConfidenceScore(
  invoice: {
    date: Date;
    amountGross: Prisma.Decimal;
    currencyCode: string;
    vendorName?: string | null;
  },
  transfer: {
    amount: Prisma.Decimal;
    currencyCode: string;
    counterparty?: string | null;
    description?: string | null;
  },
  transaction: { executedAt: Date },
  config: ReconciliationConfig
): { score: number; reason: string } {
  let score = 0;
  let reasons: string[] = [];

  // Amount matching (50 points max)
  const invoiceAmount = Math.abs(Number(invoice.amountGross));
  const transferAmount = Math.abs(Number(transfer.amount));
  const amountDiff = Math.abs(invoiceAmount - transferAmount);

  const normalizedInvoiceDate = startOfDay(invoice.date);
  const normalizedTransferDate = startOfDay(transaction.executedAt);

  if (normalizedInvoiceDate.getTime() > normalizedTransferDate.getTime()) {
    return {
      score: 0,
      reason: "invoice_date_after_transfer_date",
    };
  }

  if (amountDiff === 0) {
    score += 50;
    reasons.push("exact_amount");
  } else if (amountDiff <= config.amountToleranceFraction * invoiceAmount) {
    score += 40;
    reasons.push("amount_within_tolerance");
  } else if (amountDiff <= invoiceAmount * 0.03) {
    score += 20;
    reasons.push("amount_close");
  } else {
    return {
      score: 0,
      reason: "amount_not_close",
    };
  }

  // score += 20;
  // Currency matching (20 points max)
  // if (invoice.currencyCode === transfer.currencyCode) {
  //   score += 20;
  //   reasons.push("currency_match");
  // }

  // Date proximity (30 points max)
  const daysDiff = Math.abs((invoice.date.getTime() - transaction.executedAt.getTime()) / (1000 * 60 * 60 * 24));

  if (daysDiff <= 1) {
    score += 30;
    reasons.push("same_date");
  }
  // else if (daysDiff <= 7) {
  //   score += 25;
  //   reasons.push("within_7_days");
  // }
  else {
    const remainingRange = config.maxDaysDifference;
    const scale = (remainingRange - daysDiff) / remainingRange;
    score += 30 * Math.max(scale, 0);
    reasons.push("within_max_days");
  }

  // Vendor name similarity (30 points max)
  if (invoice.vendorName) {
    const searchText = [transfer.description, transfer.counterparty].filter(Boolean).join(" ");
    const sim = textUtils.vendorNameSimilarity(invoice.vendorName, searchText);
    if (sim >= 0.9) {
      // score += 30;
      reasons.push("vendor_exact");
    }
    // else if (sim >= 0.5) {
    //   score += 15;
    //   reasons.push("vendor_fuzzy");
    // }
    else {
      reasons.push("vendor_fuzzy");
    }
    score += 30 * sim;
  }

  // else if (daysDiff <= 1) {
  //   score += 25;
  //   reasons.push("next_day");
  // } else if (daysDiff <= 3) {
  //   score += 15;
  //   reasons.push("within_3_days");
  // } else if (daysDiff <= config.maxDaysDifference) {
  //   score += 5;
  //   reasons.push("within_week");
  // }

  return {
    score: Math.min(score, 100),
    reason: reasons.join(", "),
  };
}

/**
 * Find potential matches for unmatched invoices and transfers
 */
export async function findPotentialMatches(
  config: ReconciliationConfig & {
    invoiceId?: string;
  } = DEFAULT_CONFIG
) {
  return ResultAsync.fromPromise(
    (async () => {
      // Get unmatched invoices
      const unmatchedInvoices = await prisma.invoice.findMany({
        where: {
          reconciliations: {
            none: {},
          },
          ...(config.invoiceId ? { id: config.invoiceId } : {}),
        },
        include: {
          vendor: {
            select: { name: true },
          },
        },
      });

      // Get unmatched transfers with their transaction dates
      const unmatchedTransfers = await prisma.transfer.findMany({
        where: {
          reconciliations: {
            none: {},
          },
          transaction: {
            account: {
              type: {
                not: "WALLET",
              },
            },
          },
        },
        include: {
          transaction: {
            select: {
              executedAt: true,
              account: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
      });

      const potentialMatches: (ReconciliationMatch & {
        invoice: (typeof unmatchedInvoices)[0];
        transfer: (typeof unmatchedTransfers)[0];
      })[] = [];

      // Find matches for each invoice
      for (const invoice of unmatchedInvoices) {
        for (const transfer of unmatchedTransfers) {
          const { score, reason } = calculateConfidenceScore({ ...invoice, vendorName: invoice.vendor?.name }, transfer, transfer.transaction, config);

          // Only consider matches above a minimum threshold
          if (score >= 60) {
            potentialMatches.push({
              invoiceId: invoice.id,
              transferId: transfer.id,
              confidenceScore: score,
              matchReason: reason,
              invoice,
              transfer,
            });
          }
        }
      }

      // Sort by confidence score descending
      return potentialMatches.sort((a, b) => b.confidenceScore - a.confidenceScore);
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to find potential matches",
      err,
    })
  );
}

/**
 * Automatically match invoices and transfers based on confidence score
 */
export async function autoReconcile(
  config: ReconciliationConfig = DEFAULT_CONFIG
): Promise<ResultAsync<number, { type: "error"; message: string; err: unknown }>> {
  return ResultAsync.fromPromise(
    (async () => {
      const potentialMatchesResult = await findPotentialMatches(config);
      if (potentialMatchesResult.isErr()) {
        throw potentialMatchesResult.error;
      }

      const potentialMatches = potentialMatchesResult.value;
      const autoMatches = potentialMatches.filter((match) => match.confidenceScore >= config.minAutoMatchConfidence);

      // Create reconciliation records for auto matches
      const reconciliations = await prisma.reconciliation.createMany({
        data: autoMatches.map((match) => ({
          invoiceId: match.invoiceId,
          transferId: match.transferId,
          status: ReconciliationStatus.AUTO_MATCHED,
          confidenceScore: match.confidenceScore,
          matchReason: match.matchReason,
          isManualOverride: false,
        })),
        skipDuplicates: true,
      });

      return reconciliations.count;
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to auto reconcile",
      err,
    })
  );
}

/**
 * Manually match an invoice with a transfer
 */
export async function manualMatch(invoiceId: string, transferId: string): Promise<ResultAsync<void, { type: "error"; message: string; err: unknown }>> {
  return ResultAsync.fromPromise(
    (async () => {
      await prisma.reconciliation.upsert({
        where: {
          invoiceId,
        },
        create: {
          invoiceId,
          transferId,
          status: ReconciliationStatus.MANUALLY_MATCHED,
          isManualOverride: true,
          confidenceScore: 100,
          matchReason: "manual_match",
        },
        update: {
          status: ReconciliationStatus.MANUALLY_MATCHED,
          isManualOverride: true,
        },
      });
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to manually match",
      err,
    })
  );
}

/**
 * Manually unmatch an invoice from a transfer
 */
export async function manualUnmatch(invoiceId: string, transferId: string): Promise<ResultAsync<void, { type: "error"; message: string; err: unknown }>> {
  return ResultAsync.fromPromise(
    (async () => {
      await prisma.reconciliation.upsert({
        where: {
          invoiceId,
        },
        create: {
          invoiceId,
          transferId,
          status: ReconciliationStatus.MANUALLY_UNMATCHED,
          isManualOverride: true,
          confidenceScore: 0,
          matchReason: "manual_unmatch",
        },
        update: {
          status: ReconciliationStatus.MANUALLY_UNMATCHED,
          isManualOverride: true,
        },
      });
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to manually unmatch",
      err,
    })
  );
}

/**
 * Auto-reconcile for a specific invoice (when a new invoice is created)
 */
export async function autoReconcileForInvoice(
  invoiceId: string,
  config: ReconciliationConfig = DEFAULT_CONFIG
): Promise<ResultAsync<number, { type: "error"; message: string; err: unknown }>> {
  return ResultAsync.fromPromise(
    (async () => {
      // Get the invoice
      const invoice = await prisma.invoice.findUnique({
        where: { id: invoiceId },
        select: {
          id: true,
          date: true,
          amountGross: true,
          currencyCode: true,
          vendor: {
            select: { name: true },
          },
        },
      });

      if (!invoice) {
        throw new Error("Invoice not found");
      }

      // Get unmatched transfers
      const unmatchedTransfers = await prisma.transfer.findMany({
        where: {
          reconciliations: {
            none: {},
          },
        },
        select: {
          id: true,
          amount: true,
          currencyCode: true,
          counterparty: true,
          description: true,
          transaction: {
            select: {
              executedAt: true,
            },
          },
        },
      });

      const potentialMatches: ReconciliationMatch[] = [];

      // Find matches for this invoice
      for (const transfer of unmatchedTransfers) {
        const { score, reason } = calculateConfidenceScore({ ...invoice, vendorName: invoice.vendor?.name }, transfer, transfer.transaction, config);

        // Only consider matches above a minimum threshold
        if (score >= config.minAutoMatchConfidence) {
          potentialMatches.push({
            invoiceId: invoice.id,
            transferId: transfer.id,
            confidenceScore: score,
            matchReason: reason,
          });
        }
      }

      // Create reconciliation records for auto matches
      const reconciliations = await prisma.reconciliation.createMany({
        data: potentialMatches.map((match) => ({
          invoiceId: match.invoiceId,
          transferId: match.transferId,
          status: ReconciliationStatus.AUTO_MATCHED,
          confidenceScore: match.confidenceScore,
          matchReason: match.matchReason,
          isManualOverride: false,
        })),
        skipDuplicates: true,
      });

      return reconciliations.count;
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to auto reconcile for invoice",
      err,
    })
  );
}

/**
 * Auto-reconcile for a specific transfer (when a new transfer is created)
 */
export async function autoReconcileForTransfer(
  transferId: string,
  config: ReconciliationConfig = DEFAULT_CONFIG
): Promise<ResultAsync<number, { type: "error"; message: string; err: unknown }>> {
  return ResultAsync.fromPromise(
    (async () => {
      // Get the transfer with transaction
      const transfer = await prisma.transfer.findUnique({
        where: { id: transferId },
        select: {
          id: true,
          amount: true,
          currencyCode: true,
          counterparty: true,
          description: true,
          transaction: {
            select: {
              executedAt: true,
            },
          },
        },
      });

      if (!transfer) {
        throw new Error("Transfer not found");
      }

      // Get unmatched invoices
      const unmatchedInvoices = await prisma.invoice.findMany({
        where: {
          reconciliations: {
            none: {},
          },
        },
        select: {
          id: true,
          date: true,
          amountGross: true,
          currencyCode: true,
          vendor: {
            select: { name: true },
          },
        },
      });

      const potentialMatches: ReconciliationMatch[] = [];

      // Find matches for this transfer
      for (const invoice of unmatchedInvoices) {
        const { score, reason } = calculateConfidenceScore({ ...invoice, vendorName: invoice.vendor?.name }, transfer, transfer.transaction, config);

        // Only consider matches above a minimum threshold
        if (score >= config.minAutoMatchConfidence) {
          potentialMatches.push({
            invoiceId: invoice.id,
            transferId: transfer.id,
            confidenceScore: score,
            matchReason: reason,
          });
        }
      }

      // Create reconciliation records for auto matches
      const reconciliations = await prisma.reconciliation.createMany({
        data: potentialMatches.map((match) => ({
          invoiceId: match.invoiceId,
          transferId: match.transferId,
          status: ReconciliationStatus.AUTO_MATCHED,
          confidenceScore: match.confidenceScore,
          matchReason: match.matchReason,
          isManualOverride: false,
        })),
        skipDuplicates: true,
      });

      return reconciliations.count;
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to auto reconcile for transfer",
      err,
    })
  );
}

/**
 * Get reconciliation statistics
 */
export async function getReconciliationStats(): Promise<
  ResultAsync<
    {
      totalInvoices: number;
      totalTransfers: number;
      matchedInvoices: number;
      matchedTransfers: number;
      autoMatched: number;
      manuallyMatched: number;
      potentialMatches: number;
    },
    { type: "error"; message: string; err: unknown }
  >
> {
  return ResultAsync.fromPromise(
    (async () => {
      const [totalInvoices, totalTransfers, matchedInvoices, matchedTransfers, autoMatched, manuallyMatched, potentialMatchesResult] = await Promise.all([
        prisma.invoice.count(),
        prisma.transfer.count(),
        prisma.invoice.count({
          where: {
            reconciliations: {
              some: {
                status: {
                  in: [ReconciliationStatus.AUTO_MATCHED, ReconciliationStatus.MANUALLY_MATCHED],
                },
              },
            },
          },
        }),
        prisma.transfer.count({
          where: {
            reconciliations: {
              some: {
                status: {
                  in: [ReconciliationStatus.AUTO_MATCHED, ReconciliationStatus.MANUALLY_MATCHED],
                },
              },
            },
          },
        }),
        prisma.reconciliation.count({
          where: { status: ReconciliationStatus.AUTO_MATCHED },
        }),
        prisma.reconciliation.count({
          where: { status: ReconciliationStatus.MANUALLY_MATCHED },
        }),
        findPotentialMatches(),
      ]);

      const potentialMatches = potentialMatchesResult.isOk() ? potentialMatchesResult.value.length : 0;

      return {
        totalInvoices,
        totalTransfers,
        matchedInvoices,
        matchedTransfers,
        autoMatched,
        manuallyMatched,
        potentialMatches,
      };
    })(),
    (err) => ({
      type: "error" as const,
      message: "Failed to get reconciliation stats",
      err,
    })
  );
}
