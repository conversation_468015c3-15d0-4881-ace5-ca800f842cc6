import { z } from "zod";
import { createTR<PERSON><PERSON>outer, publicProcedure } from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { ReconciliationStatus } from "@/prisma/generated";
import {
  findPotentialMatches,
  autoReconcile,
  manualMatch,
  manualUnmatch,
  getReconciliationStats,
  calculateDetailedConfidenceScore,
  DEFAULT_CONFIG,
} from "./reconciliationService";

// Input validation schemas
const getReconciliationsSchema = z
  .object({
    limit: z.number().min(1).max(100).default(20),
    cursor: z.string().optional(),
    status: z.nativeEnum(ReconciliationStatus).optional(),
  })
  .optional()
  .default({});

const manualMatchSchema = z.object({
  invoiceId: z.string(),
  transferId: z.string(),
});

const manualUnmatchSchema = z.object({
  invoiceId: z.string(),
  transferId: z.string(),
});

const getDetailedMatchScoreSchema = z.object({
  invoiceId: z.string(),
  transferId: z.string(),
});

const getUnmatchedItemsSchema = z
  .object({
    limit: z.number().min(1).max(100).default(20),
    cursor: z.string().optional(),
    type: z.enum(["invoices", "transfers"]).optional(),
  })
  .optional()
  .default({});

// Generic pagination schema used by multiple list endpoints
const paginationSchema = z
  .object({
    limit: z.number().min(1).max(100).default(20),
    cursor: z.string().optional(),
  })
  .optional()
  .default({});

export const reconciliationRouter = createTRPCRouter({
  // Get reconciliation statistics
  getStats: publicProcedure.query(async ({ ctx }) => {
    try {
      const statsResult = await getReconciliationStats();
      if (statsResult.isErr()) {
        console.error("Failed to get reconciliation stats:", statsResult.error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get reconciliation stats",
          cause: statsResult.error,
        });
      }
      return statsResult.value;
    } catch (error) {
      console.error("Failed to get reconciliation stats:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get reconciliation stats",
        cause: error,
      });
    }
  }),

  // Get all reconciliations with pagination
  getAll: publicProcedure.input(getReconciliationsSchema).query(async ({ input, ctx }) => {
    try {
      const limit = input?.limit ?? 20;
      const cursor = input?.cursor;
      const status = input?.status;

      const reconciliations = await ctx.prisma.reconciliation.findMany({
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
        where: status ? { status } : undefined,
        orderBy: { createdAt: "desc" },
        include: {
          invoice: {
            include: {
              vendor: {
                select: {
                  name: true,
                },
              },
            },
          },
          transfer: {
            include: {
              transaction: {
                select: {
                  executedAt: true,
                  account: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      let nextCursor: string | undefined = undefined;
      if (reconciliations.length > limit) {
        const nextItem = reconciliations.pop();
        nextCursor = nextItem!.id;
      }

      return {
        reconciliations,
        nextCursor,
      };
    } catch (error) {
      console.error("Failed to fetch reconciliations:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch reconciliations",
        cause: error,
      });
    }
  }),

  // Get potential matches
  getPotentialMatches: publicProcedure.query(async ({ ctx }) => {
    try {
      const matchesResult = await findPotentialMatches();
      if (matchesResult.isErr()) {
        console.error("Failed to find potential matches:", matchesResult.error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to find potential matches",
          cause: matchesResult.error,
        });
      }

      return matchesResult.value;
    } catch (error) {
      console.error("Failed to get potential matches:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get potential matches",
        cause: error,
      });
    }
  }),

  // Get unmatched items (invoices or transfers)
  getUnmatched: publicProcedure.input(getUnmatchedItemsSchema).query(async ({ input, ctx }) => {
    try {
      const limit = input?.limit ?? 20;
      const cursor = input?.cursor;
      const type = input?.type;

      if (!type || type === "invoices") {
        const invoices = await ctx.prisma.invoice.findMany({
          take: limit + 1,
          cursor: cursor ? { id: cursor } : undefined,
          where: {
            reconciliations: {
              none: {
                status: {
                  in: [ReconciliationStatus.AUTO_MATCHED, ReconciliationStatus.MANUALLY_MATCHED],
                },
              },
            },
          },
          orderBy: { date: "desc" },
          include: {
            vendor: {
              select: {
                name: true,
              },
            },
          },
        });

        let nextCursor: string | undefined = undefined;
        if (invoices.length > limit) {
          const nextItem = invoices.pop();
          nextCursor = nextItem!.id;
        }

        return {
          type: "invoices" as const,
          items: invoices,
          nextCursor,
        };
      } else {
        const transfers = await ctx.prisma.transfer.findMany({
          take: limit + 1,
          cursor: cursor ? { id: cursor } : undefined,
          where: {
            reconciliations: {
              none: {
                status: {
                  in: [ReconciliationStatus.AUTO_MATCHED, ReconciliationStatus.MANUALLY_MATCHED],
                },
              },
            },
          },
          include: {
            transaction: {
              select: {
                executedAt: true,
                account: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
          orderBy: {
            transaction: {
              executedAt: "desc",
            },
          },
        });

        let nextCursor: string | undefined = undefined;
        if (transfers.length > limit) {
          const nextItem = transfers.pop();
          nextCursor = nextItem!.id;
        }

        return {
          type: "transfers" as const,
          items: transfers,
          nextCursor,
        };
      }
    } catch (error) {
      console.error("Failed to fetch unmatched items:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch unmatched items",
        cause: error,
      });
    }
  }),

  // Get unmatched invoices only
  getUnmatchedInvoices: publicProcedure.input(paginationSchema).query(async ({ input, ctx }) => {
    try {
      const limit = input?.limit ?? 20;
      const cursor = input?.cursor;

      const invoices = await ctx.prisma.invoice.findMany({
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
        where: {
          reconciliations: {
            none: {
              status: {
                in: [ReconciliationStatus.AUTO_MATCHED, ReconciliationStatus.MANUALLY_MATCHED],
              },
            },
          },
        },
        orderBy: { date: "desc" },
        include: {
          vendor: {
            select: {
              name: true,
            },
          },
        },
      });

      let nextCursor: string | undefined = undefined;
      if (invoices.length > limit) {
        const nextItem = invoices.pop();
        nextCursor = nextItem!.id;
      }

      return {
        items: invoices,
        nextCursor,
      } as const;
    } catch (error) {
      console.error("Failed to fetch unmatched invoices:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch unmatched invoices",
        cause: error,
      });
    }
  }),

  // Get unmatched transfers only
  getUnmatchedTransfers: publicProcedure.input(paginationSchema).query(async ({ input, ctx }) => {
    try {
      const limit = input?.limit ?? 20;
      const cursor = input?.cursor;

      const transfers = await ctx.prisma.transfer.findMany({
        take: limit + 1,
        cursor: cursor ? { id: cursor } : undefined,
        where: {
          reconciliations: {
            none: {
              status: {
                in: [ReconciliationStatus.AUTO_MATCHED, ReconciliationStatus.MANUALLY_MATCHED],
              },
            },
          },
        },
        include: {
          transaction: {
            select: {
              executedAt: true,
              account: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
        orderBy: {
          transaction: {
            executedAt: "desc",
          },
        },
      });

      let nextCursor: string | undefined = undefined;
      if (transfers.length > limit) {
        const nextItem = transfers.pop();
        nextCursor = nextItem!.id;
      }

      return {
        items: transfers,
        nextCursor,
      } as const;
    } catch (error) {
      console.error("Failed to fetch unmatched transfers:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch unmatched transfers",
        cause: error,
      });
    }
  }),

  // Run auto reconciliation
  autoReconcile: publicProcedure.mutation(async ({ ctx }) => {
    try {
      const result = await autoReconcile();
      if (result.isErr()) {
        console.error("Auto reconciliation failed:", result.error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Auto reconciliation failed",
          cause: result.error,
        });
      }
      return { matchedCount: result.value };
    } catch (error) {
      console.error("Auto reconciliation failed:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Auto reconciliation failed",
        cause: error,
      });
    }
  }),

  // Manually match invoice with transfer
  manualMatch: publicProcedure.input(manualMatchSchema).mutation(async ({ input, ctx }) => {
    try {
      const result = await manualMatch(input.invoiceId, input.transferId);
      if (result.isErr()) {
        console.error("Manual match failed:", result.error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Manual match failed",
          cause: result.error,
        });
      }
      return { success: true };
    } catch (error) {
      console.error("Manual match failed:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Manual match failed",
        cause: error,
      });
    }
  }),

  // Manually unmatch invoice from transfer
  manualUnmatch: publicProcedure.input(manualUnmatchSchema).mutation(async ({ input, ctx }) => {
    try {
      const result = await manualUnmatch(input.invoiceId, input.transferId);
      if (result.isErr()) {
        console.error("Manual unmatch failed:", result.error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Manual unmatch failed",
          cause: result.error,
        });
      }
      return { success: true };
    } catch (error) {
      console.error("Manual unmatch failed:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Manual unmatch failed",
        cause: error,
      });
    }
  }),

  // Get detailed match score breakdown
  getDetailedMatchScore: publicProcedure.input(getDetailedMatchScoreSchema).query(async ({ input, ctx }) => {
    try {
      // Fetch invoice
      const invoice = await ctx.prisma.invoice.findUnique({
        where: { id: input.invoiceId },
        include: {
          vendor: {
            select: { name: true },
          },
        },
      });

      if (!invoice) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Invoice not found",
        });
      }

      // Fetch transfer with transaction
      const transfer = await ctx.prisma.transfer.findUnique({
        where: { id: input.transferId },
        include: {
          transaction: {
            select: {
              executedAt: true,
            },
          },
        },
      });

      if (!transfer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Transfer not found",
        });
      }

      // Calculate detailed score
      const detailedScore = calculateDetailedConfidenceScore(
        {
          date: invoice.date,
          amountGross: invoice.amountGross,
          currencyCode: invoice.currencyCode,
          vendorName: invoice.vendor?.name,
        },
        {
          amount: transfer.amount,
          currencyCode: transfer.currencyCode,
          counterparty: transfer.counterparty,
          description: transfer.description,
        },
        transfer.transaction,
        DEFAULT_CONFIG
      );

      return detailedScore;
    } catch (error) {
      console.error("Failed to get detailed match score:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get detailed match score",
        cause: error,
      });
    }
  }),
});
